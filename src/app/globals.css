@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: '<PERSON> Sans', sans-serif; /* Default body font */
}

@layer base {
  :root {
    --background: 60 56% 91%; /* Off-white #F5F5DC */
    --foreground: 0 0% 10%; /* Darker foreground for readability on off-white */
    --card: 60 56% 93%; /* Slightly different from background for depth */
    --card-foreground: 0 0% 10%;
    --popover: 60 56% 91%;
    --popover-foreground: 0 0% 10%;
    --primary: 345 100% 25.1%; /* Deep burgundy #800020 */
    --primary-foreground: 0 0% 98%; /* Light color for text on primary */
    --secondary: 345 60% 45%; /* A lighter shade of burgundy for secondary elements */
    --secondary-foreground: 0 0% 98%;
    --muted: 60 30% 85%; /* Muted tone for less important text/elements */
    --muted-foreground: 0 0% 30%;
    --accent: 51 100% 50%; /* Gold #FFD700 */
    --accent-foreground: 345 100% 15%; /* Dark text on gold for contrast */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 345 20% 60%; /* Border color derived from primary */
    --input: 345 20% 70%;
    --ring: 51 100% 50%; /* Accent color for rings */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    /* Dark theme can be defined here if needed, but current proposal is light-themed */
    --background: 0 0% 10%; /* Dark background for a potential dark mode */
    --foreground: 0 0% 95%;
    --card: 0 0% 12%;
    --card-foreground: 0 0% 95%;
    --popover: 0 0% 10%;
    --popover-foreground: 0 0% 95%;
    --primary: 345 80% 55%; /* Brighter burgundy for dark mode */
    --primary-foreground: 0 0% 98%;
    --secondary: 345 50% 35%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 20%;
    --muted-foreground: 0 0% 60%;
    --accent: 51 100% 60%; /* Slightly brighter gold */
    --accent-foreground: 345 100% 15%;
    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 98%;
    --border: 345 20% 40%;
    --input: 345 20% 30%;
    --ring: 51 100% 60%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    /* Ensure scrolling behavior is smooth for better UX */
    scroll-behavior: smooth;
  }
  /* Styling for selection to match accent */
  ::selection {
    background-color: hsl(var(--accent));
    color: hsl(var(--accent-foreground));
  }
}
