'use client';

import type { Suspect } from '@/types/mystery';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { Fingerprint, Search, UserIcon } from 'lucide-react';
import Image from 'next/image';

interface SuspectListProps {
  suspects: Suspect[];
  onSelectSuspect: (suspectId: string) => void;
  onConfirmAccusation: () => void;
  selectedSuspectId: string | null;
  isAccusationMade: boolean;
}

export function SuspectList({
  suspects,
  onSelectSuspect,
  onConfirmAccusation,
  selectedSuspectId,
  isAccusationMade,
}: SuspectListProps) {
  const selectedSuspectName = suspects.find(s => s.id === selectedSuspectId)?.name;

  return (
    <section aria-labelledby="suspects-heading" className="w-full mt-8">
      <header className="mb-6 text-center">
        <h2 id="suspects-heading" className="font-headline text-3xl text-primary flex items-center justify-center gap-2">
          <UserIcon className="h-7 w-7" />
          The Usual Suspects
        </h2>
        <p className="text-muted-foreground italic">
          {isAccusationMade ? "The investigation is closed." : "One of them is the culprit. Who will you accuse?"}
        </p>
      </header>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {suspects.map((suspect) => (
          <Card
            key={suspect.id}
            className={cn(
              'shadow-lg transition-all duration-300 ease-in-out transform hover:scale-105 focus-within:ring-2 focus-within:ring-accent bg-card/70 backdrop-blur-sm',
              selectedSuspectId === suspect.id && 'ring-2 ring-accent scale-105 shadow-2xl border-accent',
              isAccusationMade && selectedSuspectId !== suspect.id && 'opacity-60 saturate-50',
              isAccusationMade && selectedSuspectId === suspect.id && 'border-accent'
            )}
            role="button"
            tabIndex={isAccusationMade ? -1 : 0}
            aria-pressed={selectedSuspectId === suspect.id}
            onClick={() => !isAccusationMade && onSelectSuspect(suspect.id)}
            onKeyDown={(e) => {
              if (!isAccusationMade && (e.key === 'Enter' || e.key === ' ')) {
                onSelectSuspect(suspect.id);
              }
            }}
          >
            <CardHeader className="flex flex-row items-start gap-4 pb-3">
              <Image 
                src={`https://placehold.co/80x80.png`}
                alt={`Mugshot of ${suspect.name}`}
                width={80}
                height={80}
                className="rounded-full border-2 border-primary/30"
                data-ai-hint={suspect.imageHint}
              />
              <div className="flex-1">
                <CardTitle className="font-headline text-xl text-primary flex items-center gap-2">
                  <Fingerprint className="h-5 w-5 text-primary/70" />
                  {suspect.name}
                </CardTitle>
                <CardDescription className="text-sm text-foreground/80 font-body pt-1">
                  {suspect.description}
                </CardDescription>
              </div>
            </CardHeader>
          </Card>
        ))}
      </div>

      {!isAccusationMade && selectedSuspectId && (
        <div className="mt-8 text-center animate-in fade-in duration-500">
          <Button
            onClick={onConfirmAccusation}
            disabled={!selectedSuspectId || isAccusationMade}
            className="bg-accent text-accent-foreground hover:bg-accent/90 text-lg px-8 py-6 rounded-lg shadow-md hover:shadow-lg transition-shadow font-headline"
            aria-label={`Accuse ${selectedSuspectName}`}
          >
            <Search className="mr-2 h-5 w-5" />
            Accuse {selectedSuspectName}
          </Button>
        </div>
      )}
    </section>
  );
}
