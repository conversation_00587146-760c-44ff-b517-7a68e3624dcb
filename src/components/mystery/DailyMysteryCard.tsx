import type { Mystery } from '@/types/mystery';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { BookOpenText } from 'lucide-react';

interface DailyMysteryCardProps {
  mystery: Mystery;
}

export function DailyMysteryCard({ mystery }: DailyMysteryCardProps) {
  return (
    <Card className="w-full shadow-xl border-primary/30 bg-card/80 backdrop-blur-sm">
      <CardHeader className="border-b border-primary/20">
        <div className="flex items-center space-x-3">
          <BookOpenText className="h-8 w-8 text-primary" />
          <CardTitle className="font-headline text-3xl md:text-4xl text-primary">{mystery.title}</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <CardDescription className="text-lg leading-relaxed whitespace-pre-line text-foreground/90 font-body">
          {mystery.story}
        </CardDescription>
      </CardContent>
    </Card>
  );
}
