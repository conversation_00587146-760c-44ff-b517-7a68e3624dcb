import type { CrimeExplanation as CrimeExplanationType } from '@/types/mystery';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Lightbulb, CheckCircle, AlertTriangle } from 'lucide-react';

interface CrimeExplanationProps {
  explanation: CrimeExplanationType;
  killerName: string;
  isCorrectGuess: boolean | null;
  accusedName?: string | null;
}

export function CrimeExplanation({ explanation, killerName, isCorrectGuess, accusedName }: CrimeExplanationProps) {
  return (
    <Card className="w-full mt-8 shadow-xl border-primary/30 bg-card/80 backdrop-blur-sm animate-slide-in-up duration-700">
      <CardHeader className="border-b border-primary/20">
         <div className="flex items-center space-x-3">
          <Lightbulb className="h-8 w-8 text-accent" />
          <CardTitle className="font-headline text-3xl md:text-4xl text-primary">The Whole Story Unveiled</CardTitle>
        </div>
        {isCorrectGuess !== null && (
            <CardDescription className="pt-2 text-lg flex items-center">
              {isCorrectGuess ? (
                <>
                  <CheckCircle className="h-6 w-6 text-green-600 mr-2" />
                  <span>You correctly identified <strong className="text-primary">{killerName}</strong> as the culprit!</span>
                </>
              ) : (
                <>
                  <AlertTriangle className="h-6 w-6 text-red-600 mr-2" />
                  <span>
                    You accused <strong className="text-primary">{accusedName || 'an innocent'}</strong>. The true culprit was <strong className="text-primary">{killerName}</strong>.
                  </span>
                </>
              )}
            </CardDescription>
          )}
      </CardHeader>
      <CardContent className="pt-6 space-y-6 text-foreground/90 font-body text-lg">
        <div>
          <h3 className="font-headline text-2xl text-primary mb-2">Motive:</h3>
          <p className="leading-relaxed">{explanation.motive}</p>
        </div>
        <div>
          <h3 className="font-headline text-2xl text-primary mb-2">Method:</h3>
          <p className="leading-relaxed">{explanation.method}</p>
        </div>
        <div>
          <h3 className="font-headline text-2xl text-primary mb-2">Key Evidence:</h3>
          <p className="leading-relaxed">{explanation.keyEvidence}</p>
        </div>
      </CardContent>
    </Card>
  );
}
