# **App Name**: DailyDeduction

## Core Features:

- Daily Mystery Presentation: Display a new murder mystery to the user each day, drawn from a firebase firestore mystery repository. Each daily mystery is the same for all players.
- Suspect Selection: Allow users to select a suspect from the list of suspects provided for each mystery.
- Killer Reveal: Upon submitting their choice, reveal whether the user's selected suspect is the actual killer.
- Explanation of Crime: Present a detailed explanation of the crime, including the method, motive, and key evidence upon selecting the killer

## Style Guidelines:

- Primary color: A deep burgundy (#800020), evoking mystery and suspense.
- Background color: Off-white (#F5F5DC) to mimic the color of aged paper, suggesting historical documents and mystery novels.
- Accent color: Gold (#FFD700) used sparingly for highlights and key interactive elements, signifying clues and importance.
- Headline font: 'Playfair' serif, for headlines and titles, for elegance and intrigue. Body font: 'PT Sans', sans-serif, for the body, for modern readability.
- Use simple, clean line icons representing elements of a mystery: magnifying glass, fingerprint, silhouette, question mark.
- Ensure the layout is clean and readable, with ample whitespace, mimicking the format of a mystery novel. Suspect selection at the bottom
- Subtle transitions for revealing information, such as the killer's identity and crime explanation. Fades, slides, or typewriter effects would enhance the storytelling.